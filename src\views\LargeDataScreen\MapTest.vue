<template>
  <div class="map-test">
    <h2>ECharts中国地图测试</h2>
    <div id="test_map" style="width: 800px; height: 600px; margin: 20px auto; border: 1px solid #ccc;"></div>
    <div class="controls">
      <button @click="loadTestData">加载测试数据</button>
      <button @click="clearMap">清空地图</button>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'MapTest',
  data() {
    return {
      testChart: null,
      testData: [
        { name: '北京', sl: 100, value: [116.46, 39.92] },
        { name: '上海', sl: 200, value: [121.48, 31.22] },
        { name: '广州', sl: 150, value: [113.23, 23.16] },
        { name: '深圳', sl: 180, value: [114.07, 22.62] },
        { name: '杭州', sl: 120, value: [120.19, 30.26] },
        { name: '成都', sl: 90, value: [104.06, 30.67] },
        { name: '西安', sl: 80, value: [108.95, 34.27] },
        { name: '武汉', sl: 110, value: [114.31, 30.52] },
      ]
    };
  },
  mounted() {
    this.initTestMap();
  },
  beforeDestroy() {
    if (this.testChart) {
      this.testChart.dispose();
    }
  },
  methods: {
    async initTestMap() {
      try {
        // 获取中国地图数据
        const chinaMapData = await this.getChinaMapData();
        
        // 注册地图
        echarts.registerMap('china', chinaMapData);
        
        // 初始化图表
        const chartDom = document.getElementById('test_map');
        this.testChart = echarts.init(chartDom);
        
        this.loadTestData();
        
      } catch (error) {
        console.error('初始化测试地图失败:', error);
      }
    },
    
    loadTestData() {
      if (!this.testChart) return;
      
      const option = {
        backgroundColor: '#f0f0f0',
        title: {
          text: 'ECharts中国地图测试',
          left: 'center',
          top: '20px',
          textStyle: {
            color: '#333',
            fontSize: 18,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            if (params.seriesType === 'map') {
              return `${params.name}<br/>项目数量: ${params.value || 0}`;
            } else if (params.seriesType === 'scatter') {
              return `${params.data.name}<br/>项目数量: ${params.data.value[2]}`;
            }
          }
        },
        visualMap: {
          min: 0,
          max: 200,
          left: 'left',
          top: 'bottom',
          text: ['高', '低'],
          inRange: {
            color: ['#e0f3ff', '#006edd']
          },
          show: true
        },
        geo: {
          map: 'china',
          roam: true,
          zoom: 1.2,
          label: {
            show: true,
            fontSize: 8,
            color: '#333'
          },
          itemStyle: {
            areaColor: '#e0f3ff',
            borderColor: '#006edd',
            borderWidth: 1
          },
          emphasis: {
            itemStyle: {
              areaColor: '#b3d9ff'
            }
          }
        },
        series: [
          {
            name: '项目分布',
            type: 'map',
            map: 'china',
            geoIndex: 0,
            data: this.getMapData(this.testData)
          },
          {
            name: '项目点',
            type: 'scatter',
            coordinateSystem: 'geo',
            data: this.testData.map(item => ({
              name: item.name,
              value: [...item.value, item.sl]
            })),
            symbol: 'circle',
            symbolSize: function(val) {
              return Math.max(val[2] / 10 + 5, 8);
            },
            itemStyle: {
              color: '#ff6b6b',
              shadowBlur: 10,
              shadowColor: '#ff6b6b'
            },
            emphasis: {
              itemStyle: {
                color: '#ff4757'
              }
            }
          }
        ]
      };
      
      this.testChart.setOption(option);
    },
    
    clearMap() {
      if (this.testChart) {
        this.testChart.clear();
      }
    },
    
    async getChinaMapData() {
      try {
        const response = await fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json');
        if (response.ok) {
          return await response.json();
        }
      } catch (error) {
        console.warn('从CDN获取地图数据失败，使用备用方案');
      }
      
      return {
        type: "FeatureCollection",
        features: []
      };
    },
    
    getMapData(data) {
      const mapData = {};
      data.forEach(item => {
        mapData[item.name] = item.sl;
      });
      
      const provinces = [
        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',
        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '台湾', '香港', '澳门'
      ];
      
      return provinces.map(province => ({
        name: province,
        value: mapData[province] || 0
      }));
    }
  }
};
</script>

<style scoped>
.map-test {
  padding: 20px;
  text-align: center;
}

.controls {
  margin-top: 20px;
}

.controls button {
  margin: 0 10px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.controls button:hover {
  background: #0056b3;
}
</style>
