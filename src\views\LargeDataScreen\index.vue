<template>
  <div class="large_data_screen">
    <div class="top_box">
      <div class="left_box1">
        <p class="time_zt">{{ time_show }}</p>
        <p class="box1">
          <span class="rq_zt">{{ date_show }}</span>
          <span class="time_xq">{{ week }}</span>
        </p>
      </div>
      <div class="zhong_box1">施恩德线上工地数据大屏</div>
      <div class="left_box1">
        <img
          class="sy_img"
          @click="toFunBtn(1)"
          src="https://cdn.juesedao.cn/mdy/6a5e0664984847388240ed30bf8b3815"
          alt=""
        />
        <img
          class="sy_img"
          @click="toFunBtn(2)"
          src="https://cdn.juesedao.cn/mdy/acd67a9a51af45888cce45dcce6d6259"
          alt=""
        />
        <img
          class="sy_img"
          @click="toFunBtn(3)"
          src="https://cdn.juesedao.cn/mdy/14ced61767a041499e6d08aae604ea06"
          alt=""
        />
      </div>
    </div>

    <div class="large_box">
      <div class="box_left">
        <div class="left_item">
          <img
            class="item_img"
            src="@/assets/images/large-data-screen/left_icon.png"
            alt=""
          />
          <span class="item_title">目标统计完成率</span>
        </div>
        <div class="left_item">
          <img
            class="item_img"
            src="@/assets/images/large-data-screen/left_icon.png"
            alt=""
          />
          <span class="item_title">目标统计完成率</span>
        </div>
        <div class="left_item">
          <img
            class="item_img"
            src="@/assets/images/large-data-screen/left_icon.png"
            alt=""
          />
          <span class="item_title">目标统计完成率</span>
        </div>
        <div class="left_item">
          <img
            class="item_img"
            src="@/assets/images/large-data-screen/left_icon.png"
            alt=""
          />
          <span class="item_title">目标统计完成率</span>
        </div>
      </div>
      <div class="box_center">
        <div class="center_content">
          <div id="map_cn" ref="echart"></div>
        </div>
      </div>
      <div class="box_right"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  data() {
    return {
      dataList: [],
      DataForPC: {},
      mapChart: null,
      time_show: '',
      date_show: '',
      week: '',
    };
  },
  watch: {
    dataList: {
      deep: true,
      handler(val, oldVal) {
        this.initMap(val);
      },
    },
  },
  mounted() {
    this.getDataForPC();
    this.updateTime();
    // 每秒更新时间
    setInterval(this.updateTime, 1000);
  },
  beforeDestroy() {
    if (this.mapChart) {
      this.mapChart.dispose();
    }
  },
  methods: {
    // 更新时间显示
    updateTime() {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, '0');
      const minutes = String(now.getMinutes()).padStart(2, '0');
      const seconds = String(now.getSeconds()).padStart(2, '0');
      this.time_show = `${hours}:${minutes}:${seconds}`;

      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const day = String(now.getDate()).padStart(2, '0');
      this.date_show = `${year}-${month}-${day}`;

      const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      this.week = weekDays[now.getDay()];
    },

    // 按钮点击事件
    toFunBtn(type) {
      console.log('按钮点击:', type);
      // 这里可以添加具体的按钮功能
    },

    getDataForPC() {
      this.$axios
        .get(`oe_getDataForPC_.csp`, { params: { dbName: "seddelivery" } })
        .then((response) => {
          console.log(response, "#");
          if (response.code === "1") {
            this.DataForPC = response;
            // 处理数据，过滤掉非省份数据
            this.dataList = response.projectNum
              .filter((item) => {
                // 过滤掉区县级数据，只保留省市级数据
                return item.name.includes('省') || item.name.includes('市') || item.name.includes('自治区');
              })
              .map((item) => {
              let name = item.name;
              // 去除“省”和“市”
              name = name.replace(/省|市|自治区|壮族|回族|维吾尔|特别行政区/g, "");
              // 修正错误的地名
              if (name === '广州') {
                name = '广东';
              }
              return {
                name: name,
                sl: item.value,
                value: item.value
              };
            });

            console.log('原始数据:', response.projectNum);
            console.log('处理后的数据:', this.dataList);
            console.log('数据点数量:', this.dataList.length);
          }
        })
        .catch((error) => {
          console.error('获取数据失败:', error);
          // 如果API失败，使用模拟数据
          this.dataList = [
            { name: '北京', sl: 100, value: 100 },
            { name: '上海', sl: 200, value: 200 },
            { name: '广东', sl: 150, value: 150 },
            { name: '浙江', sl: 120, value: 120 },
          ];
        });
    },

    // 初始化地图
    async initMap(data = []) {
      try {
        // 获取中国地图数据
        const chinaMapData = await this.getChinaMapData();

        // 注册地图
        echarts.registerMap('china', chinaMapData);

        // 初始化图表
        const chartDom = document.getElementById('map_cn');
        if (this.mapChart) {
          this.mapChart.dispose();
        }
        this.mapChart = echarts.init(chartDom);

        // 配置选项
        const option = {
          backgroundColor: 'transparent',
          title: {
            text: '全国项目分布图',
            left: 'center',
            top: '20px',
            textStyle: {
              color: '#fff',
              fontSize: 18,
              fontWeight: 'bold'
            }
          },
          tooltip: {
            trigger: 'item',
            formatter: function(params) {
              return `${params.name}<br/>项目数量: ${params.value || 0}`;
            },
            backgroundColor: 'rgba(0,0,0,0.8)',
            borderColor: '#1fc6ff',
            borderWidth: 1,
            textStyle: {
              color: '#fff'
            }
          },
          // visualMap: {
          //   min: 0,
          //   max: 300,
          //   left: 'left',
          //   top: 'bottom',
          //   text: ['高', '低'],
          //   textStyle: {
          //     color: '#fff'
          //   },
          //   inRange: {
          //     color: ['#0f4c75', '#3282b8', '#1fc6ff']
          //   },
          //   show: true
          // },

          series: [
            {
              name: '项目分布',
              type: 'map',
              map: 'china',
              roam: true,
              zoom: 1.2,
              data: this.getMapData(data),
              label: {
                show: false
              },
              itemStyle: {
                areaColor: '#0f4c75',
                borderColor: '#1fc6ff',
                borderWidth: 1
              },
              emphasis: {
                label: {
                  show: true,
                  color: '#fff'
                },
                itemStyle: {
                  areaColor: '#3282b8'
                }
              }
            }
          ]
        };

        this.mapChart.setOption(option);

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
          if (this.mapChart) {
            this.mapChart.resize();
          }
        });

      } catch (error) {
        console.error('初始化地图失败:', error);
      }
    },

    // 获取中国地图数据
    async getChinaMapData() {
      try {
        // 尝试从CDN获取中国地图数据
        const response = await fetch('https://geo.datav.aliyun.com/areas_v3/bound/100000_full.json');
        if (response.ok) {
          return await response.json();
        }
      } catch (error) {
        console.warn('从CDN获取地图数据失败，使用备用方案');
      }

      // 备用方案：返回简化的中国地图数据结构
      return {
        type: "FeatureCollection",
        features: []
      };
    },

    // 处理地图数据
    getMapData(data) {
      const mapData = {};

      // 建立数据映射，合并相同省份的数据
      data.forEach(item => {
        if (mapData[item.name]) {
          // 如果已存在该省份，累加数量
          mapData[item.name] += item.sl;
        } else {
          // 如果不存在，直接赋值
          mapData[item.name] = item.sl;
        }
      });

      console.log('地图数据映射:', mapData);

      // 中国省份列表
      const provinces = [
        '北京', '天津', '河北', '山西', '内蒙古', '辽宁', '吉林', '黑龙江',
        '上海', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南',
        '湖北', '湖南', '广东', '广西', '海南', '重庆', '四川', '贵州',
        '云南', '西藏', '陕西', '甘肃', '青海', '宁夏', '新疆', '台湾', '香港', '澳门'
      ];

      const result = provinces.map(province => ({
        name: province,
        value: mapData[province] || 0
      }));

      console.log('最终地图数据:', result);
      return result;
    }
  },
};
</script>

<style lang="scss" scoped>
.large_data_screen {
  width: 100vw;
  height: 100vh;
  // background-color: #041A21;
  background: url(@/assets/images/large-data-screen/large_bg.png) no-repeat
    center;
  background-size: cover;
  .top_box {
    width: 100%;
    height: 0.5625rem;
    background: url(https://cdn.juesedao.cn/mdy/e5551492b89946d682cc8f2d70a7979d)
      no-repeat;
    background-size: 100% 100%;
    padding: 0 0.0313rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    overflow: hidden;
    .left_box1 {
      width: 3.625rem;
      height: 100%;
      color: #1fc6ff;
      display: flex;
      align-items: center;
      padding-left: 0.25rem;
      box-sizing: border-box;

      .time_zt {
        font-size: 0.125rem;
        font-weight: bold;
        margin-right: 0.25rem;
        // position: relative;
        // ::after {
        //   content: "";
        //   width: 0.125rem;
        //   height: 0.625rem;
        //   position: absolute;
        //   top: 0;
        //   right: 0;
        //   background: red;
        // }
      }
      .box1 {
        font-size: 0.0938rem;
        color: #1fc6ff;
        position: relative;
        ::after {
          content: "";
          width: 0.0063rem;
          height: 0.125rem;
          background: #135280;
          position: absolute;
          left: -0.125rem;
          top: 50%;
          transform: translateY(-50%);
        }
        .rq_zt {
          margin-right: 0.125rem;
        }
      }
      .sy_img {
        width: 0.6875rem;
        margin-right: 0.125rem;
      }
    }
    .zhong_box1 {
      width: 8.4375rem;
      height: 100%;
      color: white;
      margin: 0 0.125rem;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.25rem;
      letter-spacing: 0.0125rem;
      font-weight: bold;
    }
  }

  .large_box {
    display: flex;
    justify-content: space-between;
    .box_left {
      flex: 2;
      .left_item {
        display: flex;
        align-items: center;
        background-image: url(@/assets/images/large-data-screen/left_item_bg.png);
        width: 355.259px;
        height: 67.618px;
        margin: 30px 0;
        background-size: cover;
        background-repeat: no-repeat;
        .item_img {
          width: 36.514px;
          height: 36.514px;
          margin-right: 15px;
        }
        .item_title {
          color: white;
          font-size: 20px;
          font-weight: 700;
          line-height: 30px;
        }
      }
    }
    .box_center {
      flex: 4;
      .center_content {
        width: 100%;
        height: 100%;
        padding: 20px;
        box-sizing: border-box;

        #map_cn {
          width: 100%;
          height: 600px;
          background: transparent;
        }
      }
    }
    .box_right {
      flex: 2;
    }
  }
}
</style>
